package com.example.mapper;

import com.example.entity.BoxFlowLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 箱子流转日志Mapper接口
 */
@Mapper
public interface BoxFlowLogMapper {

    /**
     * 插入箱子流转日志
     * @param boxFlowLog 箱子流转日志对象
     * @return 影响行数
     */
    int insert(BoxFlowLog boxFlowLog);

    /**
     * 批量插入箱子流转日志
     * @param list 箱子流转日志列表
     * @return 影响行数
     */
    int insertBatch(List<BoxFlowLog> list);

    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 箱子流转日志对象
     */
    BoxFlowLog selectById(@Param("id") Long id);

    /**
     * 根据箱子编号查询
     * @param boxNo 箱子编号
     * @return 箱子流转日志列表
     */
    List<BoxFlowLog> selectByBoxNo(@Param("boxNo") String boxNo);

    /**
     * 根据箱子唯一标识查询
     * @param boxUnid 箱子唯一标识
     * @return 箱子流转日志对象
     */
    BoxFlowLog selectByBoxUnid(@Param("boxUnid") String boxUnid);

    /**
     * 查询所有记录
     * @return 箱子流转日志列表
     */
    List<BoxFlowLog> selectAll();

    /**
     * 更新箱子流转日志
     * @param boxFlowLog 箱子流转日志对象
     * @return 影响行数
     */
    int update(BoxFlowLog boxFlowLog);

    /**
     * 根据boxUnid更新箱子流转日志
     * @param boxFlowLog 箱子流转日志对象
     * @return 影响行数
     */
    int updateByBoxUnid(BoxFlowLog boxFlowLog);

    /**
     * 清空字段（根据boxUnid）
     * @param boxUnid 箱子唯一标识
     * @return 影响行数
     */
    int clearFieldsByBoxUnid(@Param("boxUnid") String boxUnid);

    /**
     * 标记为已使用（根据boxUnid）
     * @param boxUnid 箱子唯一标识
     * @return 影响行数
     */
    int markAsUsedByBoxUnid(@Param("boxUnid") String boxUnid);

    /**
     * 标记为未使用（根据boxUnid）
     * @param boxUnid 箱子唯一标识
     * @return 影响行数
     */
    int markAsUnusedByBoxUnid(@Param("boxUnid") String boxUnid);

    /**
     * 根据ID删除
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
}
